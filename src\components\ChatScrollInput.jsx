import { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const ChatScrollInput = () => {
  const chatInputRef = useRef(null);
  const [showControls, setShowControls] = useState(true); // Development controls

  // Configuration - Easy to adjust with controls
  const [startingTop, setStartingTop] = useState(1073);
  const [startingLeft, setStartingLeft] = useState(937);
  const [triggerPoint, setTriggerPoint] = useState(690);
  const [startingScale, setStartingScale] = useState(0.55); // 55% starting size
  const [isInScrollMode, setIsInScrollMode] = useState(false);
  const [currentScrollY, setCurrentScrollY] = useState(0);

  useEffect(() => {
    const chatInput = chatInputRef.current;
    if (!chatInput) return;

    // Set initial position - PINNED to specific point in the document
    gsap.set(chatInput, {
      position: 'absolute',
      top: `${startingTop}px`, // Specific position in document flow
      left: `${startingLeft}px`, // Specific position in document flow
      xPercent: -50, // CENTER the element on the left position
      yPercent: -50, // CENTER the element on the top position
      scale: startingScale, // Start at reduced size (55%)
      zIndex: 9999,
      opacity: 1, // VISIBLE - pinned to the page
      visibility: 'visible', // VISIBLE - part of the page layout
    });

    // Wait a bit for DOM to be ready, then create smooth ScrollTrigger
    const timer = setTimeout(() => {
      // Find the Main Card in Consulting section as the end point
      const mainCard = document.querySelector('#consulting-main-card');
      let endPoint = "+=3000"; // Default fallback

      if (mainCard) {
        const cardRect = mainCard.getBoundingClientRect();
        const cardTop = cardRect.top + window.scrollY;
        endPoint = cardTop - window.innerHeight * 0.2 - 50; // End 50px earlier
      }

      // Single smooth ScrollTrigger - no jumping!
      const smoothScrollTrigger = ScrollTrigger.create({
        trigger: document.body,
        start: triggerPoint, // Start when we hit the trigger point
        end: endPoint,
        scrub: 1.5, // Smooth scrubbing
        markers: false, // Set to true for debugging - shows start/end points
        id: "smooth-chat-scroll",
        onUpdate: (self) => {
          const progress = self.progress;

          if (progress === 0) {
            // Before trigger - PINNED to document position
            gsap.set(chatInput, {
              position: 'absolute',
              top: `${startingTop}px`,
              left: `${startingLeft}px`,
              xPercent: -50,
              yPercent: -50,
              scale: startingScale, // 55% size when pinned
              opacity: 1,
              visibility: 'visible',
              force3D: true,
            });
            setIsInScrollMode(false);
            return;
          }

          // Animation started - SIMPLE LOGIC!
          setIsInScrollMode(true);

          // Get current position when animation starts (only once)
          if (progress <= 0.05) {
            const elementRect = chatInput.getBoundingClientRect();
            const currentElementCenterTop = elementRect.top + elementRect.height / 2;
            const currentElementCenterLeft = elementRect.left + elementRect.width / 2;

            // Store these positions for the entire animation
            chatInput._fixedTop = currentElementCenterTop;
            chatInput._fixedLeft = currentElementCenterLeft;
          }

          let currentScale, opacity;

          if (progress <= 0.05) {
            // INSTANT POP-OUT (first 5%)
            currentScale = 1.0; // INSTANT scale to 100% - POP OUT!
            opacity = 1;

          } else if (progress <= 0.95) {
            // STAY FIXED ON SCREEN (5% to 95%) - SIMPLE!
            currentScale = 1.0; // Stay at full size
            opacity = 1;

          } else {
            // REVERSE POP-OUT (last 5%)
            const endProgress = (progress - 0.95) / 0.05;
            currentScale = 1.0 - endProgress; // Fast scale down to 0%
            opacity = 1 - endProgress;
          }

          // Apply simple transformation - STAY AT SAME POSITION!
          gsap.set(chatInput, {
            position: 'fixed',
            top: `${chatInput._fixedTop || 0}px`,
            left: `${chatInput._fixedLeft || 0}px`,
            xPercent: -50,
            yPercent: -50,
            scale: currentScale,
            opacity: opacity,
            force3D: true,
          });
        },
        onComplete: () => {
          // Reset back to original pinned position
          gsap.set(chatInput, {
            position: 'absolute',
            top: `${startingTop}px`,
            left: `${startingLeft}px`,
            xPercent: -50,
            yPercent: -50,
            scale: startingScale, // Back to starting scale (55%)
            opacity: 1,
            visibility: 'visible',
          });
          setIsInScrollMode(false);
        }
      });

      // Store for cleanup
      chatInputRef.current.scrollTrigger = smoothScrollTrigger;
    }, 500);

    // Real-time scroll position updater for controls
    const handleScroll = () => {
      setCurrentScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    // Cleanup function
    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleScroll);
      if (chatInputRef.current?.scrollTrigger) {
        chatInputRef.current.scrollTrigger.kill();
      }
    };
  }, [startingTop, startingLeft, triggerPoint, startingScale]); // Re-run when positions or scale change

  return (
    <>
      {/* Development Controls */}
      {showControls && (
        <div
          className="fixed top-4 right-4 bg-black/90 text-white p-4 rounded-lg z-[10000] font-mono text-sm"
          style={{ width: '300px' }}
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-green-400 font-bold">Chat Position Controls</h3>
            <button
              onClick={() => setShowControls(false)}
              className="text-red-400 hover:text-red-300"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-xs text-gray-300 mb-1">Starting Top: {startingTop}px</label>
              <input
                type="range"
                min="0"
                max="2000"
                value={startingTop}
                onChange={(e) => setStartingTop(Number(e.target.value))}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-xs text-gray-300 mb-1">Starting Left: {startingLeft}px</label>
              <input
                type="range"
                min="0"
                max="1500"
                value={startingLeft}
                onChange={(e) => setStartingLeft(Number(e.target.value))}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-xs text-gray-300 mb-1">Trigger Point: {triggerPoint}px</label>
              <input
                type="range"
                min="0"
                max="2000"
                value={triggerPoint}
                onChange={(e) => setTriggerPoint(Number(e.target.value))}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-xs text-gray-300 mb-1">Starting Scale: {(startingScale * 100).toFixed(0)}%</label>
              <input
                type="range"
                min="0.3"
                max="1.0"
                step="0.05"
                value={startingScale}
                onChange={(e) => setStartingScale(Number(e.target.value))}
                className="w-full"
              />
            </div>

            <div className="text-xs text-gray-400 mt-2 space-y-1">
              <div className="flex justify-between">
                <span>Scroll:</span>
                <span className={`font-bold ${currentScrollY >= triggerPoint - 1 ? 'text-green-400' : 'text-red-400'}`}>
                  {Math.round(currentScrollY)}px
                </span>
              </div>
              <div className="flex justify-between">
                <span>Trigger:</span>
                <span className="text-yellow-400">{triggerPoint}px</span>
              </div>
              <div className="flex justify-between">
                <span>Status:</span>
                <span className={`font-bold ${currentScrollY >= triggerPoint ? 'text-blue-400' : 'text-green-400'}`}>
                  {currentScrollY >= triggerPoint ? 'ANIMATING' : 'PINNED'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Mode:</span>
                <span className={isInScrollMode ? 'text-blue-400' : 'text-gray-400'}>
                  {isInScrollMode ? 'Fixed on Screen' : 'Pinned to Document'}
                </span>
              </div>
            </div>

            <button
              onClick={() => {
                console.log('Current settings (CENTER-CENTER positioning):', {
                  startingTop,
                  startingLeft,
                  triggerPoint,
                  startingScale: `${(startingScale * 100).toFixed(0)}%`,
                  positioning: 'center-center (xPercent: -50, yPercent: -50)'
                });
              }}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-2 rounded text-xs"
            >
              Log Settings
            </button>
          </div>
        </div>
      )}

      {/* Grid Overlay for positioning */}
      {showControls && (
        <div className="fixed inset-0 pointer-events-none z-[9998]">
          {/* Vertical grid lines */}
          {Array.from({ length: 20 }, (_, i) => (
            <div
              key={`v-${i}`}
              className="absolute top-0 bottom-0 w-px bg-red-500/20"
              style={{ left: `${i * 100}px` }}
            />
          ))}
          {/* Horizontal grid lines */}
          {Array.from({ length: 30 }, (_, i) => (
            <div
              key={`h-${i}`}
              className="absolute left-0 right-0 h-px bg-red-500/20"
              style={{ top: `${i * 100}px` }}
            />
          ))}
          {/* Position indicator with scale preview - CENTER POSITIONED */}
          <div
            className="absolute bg-yellow-400/30 border-2 border-yellow-400 rounded-lg flex items-center justify-center"
            style={{
              top: `${startingTop - (25 * startingScale)}px`,
              left: `${startingLeft - (224 * startingScale)}px`,
              width: `${448 * startingScale}px`,
              height: `${50 * startingScale}px`,
              transform: 'translate(-50%, -50%)', // CENTER the indicator
              boxShadow: '0 0 15px rgba(255, 255, 0, 0.6)'
            }}
          >
            <span className="text-yellow-400 text-xs font-bold">
              {(startingScale * 100).toFixed(0)}%
            </span>
          </div>
          <div
            className="absolute text-yellow-400 text-xs font-bold"
            style={{
              top: `${startingTop + (25 * startingScale) + 5}px`,
              left: `${startingLeft}px`,
              transform: 'translateX(-50%)' // CENTER the text
            }}
          >
            START POSITION (CENTER)
          </div>
        </div>
      )}

      {/* Chat Input */}
      <div
        ref={chatInputRef}
        className="chat-scroll-input pointer-events-auto"
        style={{
          width: '448px',
          height: '50px',
          filter: 'drop-shadow(0 10px 25px rgba(0, 0, 0, 0.4))',
          willChange: 'transform',
        }}
      >
        <svg
          width="448"
          height="50"
          viewBox="0 0 448 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full cursor-pointer hover:scale-105 transition-all duration-300 ease-out"
        >
          <rect width="448" height="50" rx="16" fill="#111216"/>
          <rect x="0.25" y="0.25" width="447.5" height="49.5" rx="15.75" stroke="#787C87" strokeOpacity="0.25" strokeWidth="0.5"/>
          <path d="M16.2266 24.998H28.5734" stroke="#ADB0B9" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M22.3984 18.8262V31.173" stroke="#ADB0B9" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <circle cx="422" cy="25" r="16" fill="#15161A"/>
          <path d="M416.9 22.3758V26.6214C416.9 26.7594 416.831 26.8973 416.695 26.9929C416.572 27.099 416.395 27.1521 416.217 27.1521C416.04 27.1521 415.862 27.099 415.74 26.9929C415.603 26.8973 415.535 26.7594 415.535 26.6214V22.3758C415.535 22.2379 415.603 22.0999 415.74 22.0044C415.862 21.8982 416.04 21.8452 416.217 21.8452C416.395 21.8452 416.572 21.8982 416.695 22.0044C416.831 22.0999 416.9 22.2379 416.9 22.3758ZM419.628 17.5996C419.451 17.5996 419.273 17.6527 419.151 17.7588C419.014 17.8544 418.946 17.9923 418.946 18.1303V30.8669C418.946 31.0049 419.014 31.1429 419.151 31.2384C419.273 31.3445 419.451 31.3976 419.628 31.3976C419.806 31.3976 419.983 31.3445 420.106 31.2384C420.242 31.1429 420.31 31.0049 420.31 30.8669V18.1303C420.31 17.9923 420.242 17.8544 420.106 17.7588C419.983 17.6527 419.806 17.5996 419.628 17.5996ZM423.039 19.7224C422.862 19.7224 422.684 19.7755 422.561 19.8816C422.425 19.9771 422.357 20.1151 422.357 20.2531V28.7442C422.357 28.8821 422.425 29.0201 422.561 29.1156C422.684 29.2218 422.862 29.2749 423.039 29.2749C423.217 29.2749 423.394 29.2218 423.517 29.1156C423.653 29.0201 423.721 28.8821 423.721 28.7442V20.2531C423.721 20.1151 423.653 19.9771 423.517 19.8816C423.394 19.7755 423.217 19.7224 423.039 19.7224ZM426.45 21.8452C426.273 21.8452 426.095 21.8982 425.972 22.0044C425.836 22.0999 425.768 22.2379 425.768 22.3758V26.6214C425.768 26.7594 425.836 26.8973 425.972 26.9929C426.095 27.099 426.273 27.1521 426.45 27.1521C426.627 27.1521 426.805 27.099 426.928 26.9929C427.064 26.8973 427.132 26.7594 427.132 26.6214V22.3758C427.132 22.2379 427.064 22.0999 426.928 22.0044C426.805 21.8982 426.627 21.8452 426.45 21.8452ZM429.861 20.7838C429.684 20.7838 429.506 20.8369 429.383 20.943C429.247 21.0385 429.179 21.1765 429.179 21.3145V27.6828C429.179 27.8208 429.247 27.9587 429.383 28.0542C429.506 28.1604 429.684 28.2135 429.861 28.2135C430.038 28.2135 430.216 28.1604 430.339 28.0542C430.475 27.9587 430.543 27.8208 430.543 27.6828V21.3145C430.543 21.1765 430.475 21.0385 430.339 20.943C430.216 20.8369 430.038 20.7838 429.861 20.7838Z" fill="#ADB0B9"/>
        </svg>
      </div>

      {/* Toggle Controls Button (when hidden) */}
      {!showControls && (
        <button
          onClick={() => setShowControls(true)}
          className="fixed top-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full z-[10000] text-xs"
        >
          ⚙️
        </button>
      )}
    </>
  );
};

export default ChatScrollInput;
