import Navigation from './components/Navigation'
import Hero from './components/Hero'
import Banner from './components/Banner'
import Problem from './components/Problem'
import Consulting from './components/Consulting'
import Consulting_Features from './components/Consulting_Features'
import Solutions from './components/Solutions'
import VideoTransition from './components/VideoTransition'
import Niches from './components/Niches'
import HowItWorks from './components/HowItWorks'
import CTA from './components/CTA'
import Footer from './components/Footer'
import ChatScrollInput from './components/ChatScrollInput'

function App() {
  return (
    <div className="min-h-screen bg-[#070709]">
      <div className="relative">
        <Navigation />
        <Hero />
      </div>
      <Banner />
      <Problem />
      <Consulting />
      <Consulting_Features />
      <VideoTransition />
      <Niches />
      <Solutions />
      <HowItWorks />
      <CTA />
      <Footer />

      {/* Chat Scroll Input - Fixed positioned overlay */}
      <ChatScrollInput />
    </div>
  )
}

export default App
