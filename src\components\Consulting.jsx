
import { useRive } from '@rive-app/react-canvas';

const Consulting = () => {
  // Rive animations
  const { RiveComponent: BgLinesRive } = useRive({
    src: '/rive/bg-lines.riv',
    autoplay: true,
  });

  const { RiveComponent: BoardRive } = useRive({
    src: '/rive/board.riv',
    autoplay: true,
  });

  return (
    <section data-section="consulting" className="min-h-[250vh] bg-[#070709] text-white px-4 relative">
      {/* Background Layers */}
      <div className="absolute inset-0 z-0">
        {/* First Layer: bg-lines background */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: 'url(/rive/bg-lines-bg.avif)',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundSize: 'auto'
          }}
        />

        {/* Second Layer: bg-lines Rive animation */}
        <div className="absolute inset-0">
          <BgLinesRive />
        </div>

        {/* Third Layer: board background */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: 'url(/rive/board-animation-bg.png)',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundSize: 'auto'
          }}
        />

        {/* Fourth Layer: board Rive animation */}
        <div className="absolute inset-0">
          <BoardRive />
        </div>
      </div>

      <div className="max-w-[960px] mx-auto pt-[calc(250vh-600px)] relative z-10">
        {/* Header Section */}
        <div className="mb-12">
          <span className="font-['Geist'] font-light text-sm text-white tracking-[0.02em] pb-3 block">
            UNSERE LÖSUNG
          </span>
          <h2 className="font-['Geist'] font-medium text-[48px] text-white tracking-[-0.02em] text-left mb-6 leading-tight">
          Consulting that <br />turns AI into outcomes
          </h2>
          <p className="font-['Geist'] font-normal text-base text-[#ADB0B9] max-w-[585px] pb-[54px]">
          Gemeinsam analysieren wir deine Abläufe, finden die größten Hebel für Automatisierungen und entwickeln eine Roadmap, die AI in echte Ergebnisse übersetzt. Effizienter arbeiten, Kosten senken, Wachstum steigern.
          </p>
        </div>

        {/* Chat Input Target Position - Invisible marker for animation */}
        <div id="chat-input-target" className="w-[448px] h-[50px] mx-auto mb-8 opacity-0 pointer-events-none">
          {/* This invisible div marks where the chat input should end up */}
        </div>

        {/* Main Card */}
        <div id="consulting-main-card" className="bg-[#0D0E12] rounded-2xl border border-[rgba(120,124,135,0.25)] border-[0.5px] w-full h-[550px] p-2">
          <div className="w-full h-full relative rounded-lg overflow-hidden">
            <video 
              src="/consultingcall.mp4" 
              className="w-full h-full object-cover"
              autoPlay
              loop
              muted
              playsInline
            />
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/30 flex flex-col pointer-events-none">
            {/* Top Left - Meeting Info */}
            <div className="absolute left-5 top-5 flex flex-col">
              <span className="text-lg font-medium leading-snug tracking-tight text-white/90">Consulting Meeting</span>
              <span className="mt-0.5 flex items-center gap-x-1.5 text-sm font-medium leading-snug tracking-tight text-white/60">
                <img src="/consulting/icon_participants.svg" alt="Participants" className="w-3 h-3 opacity-60" />
                4 Teilnehmer
              </span>
            </div>

            {/* Right Side - Participant Groups */}
            <div className="absolute h-[20.782%] w-[20.833%] right-[1.157%] top-[2.25%]">
              <div className="absolute right-[3.333%] top-[5.94%] flex h-[19.802%] w-[11.111%] items-center justify-center rounded-full bg-[#3d7eff] shadow-[0px_4px_6px_0px_#00000026]">
                <img src="/consulting/microfone.svg" alt="Participant" className="h-1/2 w-1/2" />
              </div>
              <span className="absolute bottom-[5.94%] left-[3.333%] flex items-center gap-x-1 text-xs font-medium leading-snug tracking-tight text-white/80">
                <img src="/consulting/Person.svg" alt="" className="w-2 h-2" /> Alex Held
              </span>
            </div>

            <div className="absolute h-[20.782%] w-[20.833%] right-[1.157%] top-[25.5%]">
              <div className="absolute right-[3.333%] top-[5.94%] flex h-[19.802%] w-[11.111%] items-center justify-center rounded-full bg-[#989fa9] shadow-[0px_4px_6px_0px_#00000026]">
                <img src="/consulting/mute.svg" alt="Participant" className="h-1/2 w-1/2" />
              </div>
              <span className="absolute bottom-[5.94%] left-[3.333%] flex items-center gap-x-1 text-xs font-medium leading-snug tracking-tight text-white/80">
                <img src="/consulting/Person.svg" alt="" className="w-2 h-2" /> Lea Müller
              </span>
            </div>

            <div className="absolute h-[20.782%] w-[20.833%] right-[1.157%] top-[49%]">
              <div className="absolute right-[3.333%] top-[5.94%] flex h-[19.802%] w-[11.111%] items-center justify-center rounded-full bg-[#3d7eff] shadow-[0px_4px_6px_0px_#00000026]">
                <img src="/consulting/microfone.svg" alt="Participant" className="h-1/2 w-1/2" />
              </div>
              <span className="absolute bottom-[5.94%] left-[3.333%] flex items-center gap-x-1 text-xs font-medium leading-snug tracking-tight text-white/80">
                <img src="/consulting/Person.svg" alt="" className="w-2 h-2" /> Julia Schmidt
              </span>
            </div>

            {/* Bottom Center - Action Buttons */}
            <div className="absolute inset-x-0 bottom-5 flex justify-center gap-x-3.5">
              <div className="flex aspect-square h-11 items-center justify-center rounded-full shadow-[0px_4px_16px_0px_#00000059] bg-[#68686a]">
                <img src="/consulting/settings1.svg" alt="" className="w-5 h-5" />
              </div>
              <div className="flex aspect-square h-11 items-center justify-center rounded-full shadow-[0px_4px_16px_0px_#00000059] bg-[#68686a]">
                <img src="/consulting/mute.svg" alt="" className="w-5 h-5" />
              </div>
              <div className="flex aspect-square h-11 items-center justify-center rounded-full shadow-[0px_4px_16px_0px_#00000059] bg-[#ff4d4d]">
                <img src="/consulting/close.svg" alt="" className="w-5 h-5" />
              </div>
              <div className="flex aspect-square h-11 items-center justify-center rounded-full shadow-[0px_4px_16px_0px_#00000059] bg-[#68686a]">
                <img src="/consulting/camera.svg" alt="" className="w-5 h-5" />
              </div>
              <div className="flex aspect-square h-11 items-center justify-center rounded-full shadow-[0px_4px_16px_0px_#00000059] bg-[#68686a]">
                <img src="/consulting/fullscreen.svg" alt="" className="w-5 h-5" />
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Consulting;
